<template>
	<view class="order-card">
		<!-- 订单头部信息 -->
		<view class="order-header">
			<view class="order-info">
				<view class="order-no">{{ getOrderNo() }}</view>
				<view class="order-type-badge fulfillment">履约</view>
				<view class="order-priority" v-if="orderData.priority" :class="'priority-' + orderData.priority">
					{{
						orderData.priority === 'high' ? '高' :
						orderData.priority === 'low' ? '低' : '中'
					}}
				</view>
			</view>
			<view class="order-quantity">
				<text class="quantity-main">{{ getMainQuantity() }}</text>
				<text class="quantity-completed" v-if="getCompletedQuantity()">
					已完成: {{ getCompletedQuantity() }}
				</text>
			</view>
		</view>

		<!-- 轮播容器 -->
		<swiper
			class="order-swiper"
			:indicator-dots="true"
			:autoplay="false"
			:circular="false"
			indicator-color="rgba(0, 0, 0, .3)"
			indicator-active-color="#409eff"
			@change="handleSwiperChange"
		>
			<!-- 第一页：订单信息、产品信息、数量、需求状态 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">基本信息</view>

					<!-- 订单信息 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="file-text" size="16" color="#409eff"></uv-icon>
							<text class="section-label">订单信息</text>
						</view>
						<view class="info-grid">
							<view class="info-item">
								<text class="info-label">客户名称</text>
								<text class="info-value">{{ getCustomerName() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">下单时间</text>
								<text class="info-value">{{ formatDate(getOrderDate()) }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">发货时间</text>
								<text class="info-value">{{ formatDate(getDeliveryTime()) }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">要求</text>
								<text class="info-value">{{ getRequirement() }}</text>
							</view>
						</view>
					</view>

					<!-- 产品信息 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="shopping-bag" size="16" color="#409eff"></uv-icon>
							<text class="section-label">产品信息</text>
						</view>
						<view class="info-grid">
							<view class="info-item">
								<text class="info-label">产品名称</text>
								<text class="info-value">{{ getProductName() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">产品编码</text>
								<text class="info-value">{{ getProductCode() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">规格</text>
								<text class="info-value">{{ getProductSpec() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">数量</text>
								<text class="info-value">{{ getMainQuantity() }}</text>
							</view>
						</view>
					</view>

					<!-- 需求状态 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="checkmark-circle" size="16" color="#409eff"></uv-icon>
							<text class="section-label">需求状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="requestStatusClass">
									{{ requestStatusText }}
								</view>
							</view>
							<view class="status-item" v-if="getRequestCreateTime()">
								<text class="status-label">创建时间</text>
								<text class="status-value">{{ formatDate(getRequestCreateTime()) }}</text>
							</view>
							<view class="status-item" v-if="getRequestConfirmTime()">
								<text class="status-label">BOM确认时间</text>
								<text class="status-value">{{ formatDate(getRequestConfirmTime()) }}</text>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第二页：原料库存 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">原料库存</view>

					<view class="info-section">
						<view class="section-header">
							<uv-icon name="layers" size="16" color="#409eff"></uv-icon>
							<text class="section-label">库存状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="materialStatusClass">
									{{ materialStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">库存进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="materialProgressClass" :style="{ width: materialProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ materialProgress }}%</text>
								</view>
							</view>
						</view>

						<!-- 原料详情 -->
						<view class="material-list" v-if="materialList.length > 0">
							<view class="material-item" v-for="(material, index) in materialList" :key="index">
								<view class="material-info">
									<text class="material-name">{{ material.fullCode }} {{ material.name }}</text>
									<text class="material-quantity">{{ material.pendingQty || 0 }} {{ material.unitName || '' }}</text>
								</view>
								<view class="material-status" :class="material.shortage ? 'shortage' : 'sufficient'">
									{{ material.shortage ? ('缺料' + (material.shortageQty || 0)) : '已备齐' }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无原料信息</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第三页：采购 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">采购</view>

					<view class="info-section">
						<view class="section-header">
							<uv-icon name="shopping-cart" size="16" color="#409eff"></uv-icon>
							<text class="section-label">采购状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="procurementStatusClass">
									{{ procurementStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">采购进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="procurementProgressClass" :style="{ width: procurementProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ procurementProgress }}%</text>
								</view>
							</view>
						</view>

						<!-- 采购详情 -->
						<view class="purchase-list" v-if="purchaseList.length > 0">
							<view class="purchase-item" v-for="(purchase, index) in purchaseList" :key="index">
								<view class="purchase-info">
									<text class="purchase-time">{{ formatDate(purchase.purchaseTime) }}</text>
									<text class="purchase-material">{{ purchase.materialCode }} {{ purchase.materialName }}</text>
								</view>
								<view class="purchase-status" :class="[
									purchase.purchaseStatus === '0' ? 'status-pending' :
									purchase.purchaseStatus === '1' ? 'status-processing' :
									purchase.purchaseStatus === '2' ? 'status-purchased' :
									purchase.purchaseStatus === '3' ? 'status-completed' :
									purchase.purchaseStatus === '4' ? 'status-warehoused' : 'status-pending'
								]">
									{{ getPurchaseItemStatusText(purchase.purchaseStatus) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无采购信息</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第四页：计划 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">生产计划</view>

					<view class="info-section">
						<view class="section-header">
							<uv-icon name="calendar" size="16" color="#409eff"></uv-icon>
							<text class="section-label">计划状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="productionPlanStatusClass">
									{{ productionPlanStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">计划进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="productionPlanProgressClass" :style="{ width: productionPlanProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ productionPlanProgress }}%</text>
								</view>
							</view>
						</view>

						<!-- 计划详情 -->
						<view class="plan-info">
							<view class="plan-item">
								<text class="plan-label">车间</text>
								<text class="plan-value">{{ workshop }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">产线</text>
								<text class="plan-value">{{ productionLine }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">负责人</text>
								<text class="plan-value">{{ foreman }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">计划数量</text>
								<text class="plan-value">{{ planQuantity }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">已完成</text>
								<text class="plan-value">{{ fulfilledQuantity }}</text>
							</view>
						</view>

						<!-- 计划安排 -->
						<view class="schedule-list" v-if="scheduleList.length > 0">
							<view class="schedule-title">计划安排</view>
							<view class="schedule-item" v-for="(schedule, index) in scheduleList" :key="index">
								<view class="schedule-info">
									<text class="schedule-stage">第{{ index + 1 }}阶段</text>
									<text class="schedule-time">{{ formatDate(schedule.startTime) }} ~ {{ formatDate(schedule.endTime) }}</text>
									<text class="schedule-quantity">计划: {{ schedule.quantity || 0 }}{{ unitName }}</text>
									<text class="schedule-fulfilled">已完成: {{ schedule.fulfilledQty || 0 }}{{ unitName }}</text>
								</view>
								<view class="schedule-status" :class="[
									schedule.status === '0' ? 'status-pending' :
									schedule.status === '1' ? 'status-processing' :
									schedule.status === '2' ? 'status-paused' :
									schedule.status === '3' ? 'status-completed' :
									schedule.status === '4' ? 'status-cancelled' : 'status-pending'
								]">
									{{ getScheduleStatusText(schedule.status) }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第五页：生产、质检、入库、发货、出库 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">执行状态</view>

					<!-- 生产执行 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="settings" size="16" color="#409eff"></uv-icon>
							<text class="section-label">生产执行</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="productionExecutionStatusClass">
									{{ productionExecutionStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">生产进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="productionExecutionProgressClass" :style="{ width: productionExecutionProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ productionExecutionProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已生产</text>
								<text class="status-value">{{ productionQuantity }}</text>
							</view>
						</view>
					</view>

					<!-- 质检 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="checkmark-circle" size="16" color="#409eff"></uv-icon>
							<text class="section-label">质检</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="qualityInspectionStatusClass">
									{{ qualityInspectionStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">质检进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="qualityInspectionProgressClass" :style="{ width: qualityInspectionProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ qualityInspectionProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已质检</text>
								<text class="status-value">{{ qualityInspectedQuantity }}</text>
							</view>
						</view>
					</view>

					<!-- 入库 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="archive" size="16" color="#409eff"></uv-icon>
							<text class="section-label">入库</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="warehousingStatusClass">
									{{ warehousingStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">入库进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="warehousingProgressClass" :style="{ width: warehousingProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ warehousingProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已入库</text>
								<text class="status-value">{{ warehouseInQuantity }}</text>
							</view>
						</view>
					</view>

					<!-- 发货 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="truck" size="16" color="#409eff"></uv-icon>
							<text class="section-label">发货</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="deliveryStatusClass">
									{{ deliveryStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">发货进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="deliveryProgressClass" :style="{ width: deliveryProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ deliveryProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已发货</text>
								<text class="status-value">{{ deliveredQuantity }}</text>
							</view>
						</view>
					</view>

					<!-- 出库 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="share" size="16" color="#409eff"></uv-icon>
							<text class="section-label">出库</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="outboundStatusClass">
									{{ outboundStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">出库进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="outboundProgressClass" :style="{ width: outboundProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ outboundProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已出库</text>
								<text class="status-value">{{ warehouseOutQuantity }}</text>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>

		<!-- 订单底部操作 -->
		<view class="order-footer">
			<view class="footer-left">
				<view class="order-status" :class="orderStatusClass">
					{{ orderStatusText }}
				</view>
				<view class="order-actions">
					<uv-button
						size="mini"
						type="primary"
						plain
						@click.stop="handleOrderDetail"
					>
						详情
					</uv-button>
					<uv-button
						v-if="canEditOrder"
						size="mini"
						type="warning"
						plain
						@click.stop="handleEdit"
					>
						编辑
					</uv-button>
				</view>
			</view>
			<view class="footer-right">
				<view class="overdue-info" v-if="isOrderOverdue">
					<uv-icon name="warning" size="12" color="#f56c6c"></uv-icon>
					<text class="overdue-text">逾期</text>
				</view>
			</view>
		</view>

		<!-- 异常提示 -->
		<view v-if="isOrderException" class="exception-alert">
			<uv-icon name="error-circle" size="14" color="#f56c6c"></uv-icon>
			<text class="exception-text">{{ exceptionMessage }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'OrderProcessItem',
	props: {
		orderData: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			currentSwiperIndex: 0
		}
	},
	computed: {
		// ===== 基本信息计算属性 =====
		orderNo() {
			return this.orderData.salesOrderInfo?.orderNo || this.orderData.orderNo || 'N/A'
		},
		customerName() {
			return this.orderData.salesOrderInfo?.customer?.name || this.orderData.customerName || '未知客户'
		},
		orderDate() {
			return this.orderData.salesOrderInfo?.orderDate || this.orderData.orderDate
		},
		deliveryTime() {
			return this.orderData.salesOrderInfo?.deliveryTime || this.orderData.deliveryTime
		},
		requirement() {
			return this.orderData.salesOrderInfo?.requirement || this.orderData.salesOrderInfo?.remark || this.orderData.requirement || '-'
		},
		productName() {
			return this.orderData.salesOrderInfo?.product?.name || this.orderData.productInfo || '暂无产品信息'
		},
		productCode() {
			return this.orderData.salesOrderInfo?.product?.fullCode || this.orderData.productCode || '-'
		},
		productSpec() {
			return this.orderData.salesOrderInfo?.product?.spec || this.orderData.productSpec || '-'
		},
		unitName() {
			return this.orderData.salesOrderInfo?.product?.unitName || this.orderData.unitName || ''
		},
		mainQuantity() {
			const quantity = this.orderData.salesOrderInfo?.product?.quantity || this.orderData.quantity || 0
			return `${quantity}${this.unitName}`
		},
		completedQuantity() {
			const quantity = this.orderData.outbound?.quantity || this.orderData.completedQuantity || 0
			return quantity > 0 ? `${quantity}${this.unitName}` : ''
		},

		// ===== 需求状态计算属性 =====
		requestStatusText() {
			const status = this.orderData.request?.status || '0'
			const statusMap = {
				'0': '待分析',
				'1': '已确认',
				'2': '进行中',
				'3': '已完成'
			}
			return statusMap[status] || '未知'
		},
		requestStatusClass() {
			const status = this.orderData.request?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-confirmed',
				'2': 'status-processing',
				'3': 'status-completed'
			}
			return classMap[status] || 'status-pending'
		},

		// ===== 原料库存计算属性 =====
		materialStatusText() {
			const status = this.orderData.materialInventory?.status || '0'
			const statusMap = {
				'0': '待分析',
				'1': '欠缺',
				'2': '已满足'
			}
			return statusMap[status] || '未知'
		},
		materialStatusClass() {
			const status = this.orderData.materialInventory?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-shortage',
				'2': 'status-sufficient'
			}
			return classMap[status] || 'status-pending'
		},
		materialProgress() {
			return Math.round(this.orderData.materialInventory?.progress || 0)
		},
		materialProgressClass() {
			const progress = this.materialProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		materialList() {
			return this.orderData.materialInventory?.rawMaterials || []
		},

		// ===== 采购计算属性 =====
		procurementStatusText() {
			const status = this.orderData.procurement?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		procurementStatusClass() {
			const status = this.orderData.procurement?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		procurementProgress() {
			return Math.round(this.orderData.procurement?.progress || 0)
		},
		procurementProgressClass() {
			const progress = this.procurementProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		purchaseList() {
			return this.orderData.procurement?.purchaseItems || []
		},

		// ===== 生产计划计算属性 =====
		productionPlanStatusText() {
			const status = this.orderData.productionPlan?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		productionPlanStatusClass() {
			const status = this.orderData.productionPlan?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		productionPlanProgress() {
			return Math.round(this.orderData.productionPlan?.progress || 0)
		},
		productionPlanProgressClass() {
			const progress = this.productionPlanProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		workshop() {
			return this.orderData.productionPlan?.workshop || '-'
		},
		productionLine() {
			return this.orderData.productionPlan?.productionLine || '-'
		},
		foreman() {
			return this.orderData.productionPlan?.foreman || '-'
		},
		planQuantity() {
			const quantity = this.orderData.productionPlan?.quantity || 0
			return `${quantity}${this.unitName}`
		},
		fulfilledQuantity() {
			const quantity = this.orderData.productionPlan?.fulfilledQty || 0
			return `${quantity}${this.unitName}`
		},
		scheduleList() {
			return this.orderData.productionPlan?.schedule || []
		},

		// ===== 生产执行计算属性 =====
		productionExecutionStatusText() {
			const status = this.orderData.productionExecution?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		productionExecutionStatusClass() {
			const status = this.orderData.productionExecution?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		productionExecutionProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const productionQuantity = this.orderData.productionExecution?.quantity || 0
			return totalQuantity > 0 ? Math.round((productionQuantity / totalQuantity) * 100) : 0
		},
		productionExecutionProgressClass() {
			const progress = this.productionExecutionProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		productionQuantity() {
			const quantity = this.orderData.productionExecution?.quantity || 0
			return `${quantity}${this.unitName}`
		},

		// ===== 质检计算属性 =====
		qualityInspectionStatusText() {
			const status = this.orderData.qualityInspection?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		qualityInspectionStatusClass() {
			const status = this.orderData.qualityInspection?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		qualityInspectionProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const inspectedQuantity = this.orderData.qualityInspection?.quantity || 0
			return totalQuantity > 0 ? Math.round((inspectedQuantity / totalQuantity) * 100) : 0
		},
		qualityInspectionProgressClass() {
			const progress = this.qualityInspectionProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		qualityInspectedQuantity() {
			const quantity = this.orderData.qualityInspection?.quantity || 0
			return `${quantity}${this.unitName}`
		},

		// ===== 入库计算属性 =====
		warehousingStatusText() {
			const status = this.orderData.warehousing?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		warehousingStatusClass() {
			const status = this.orderData.warehousing?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		warehousingProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const warehouseQuantity = this.orderData.warehousing?.quantity || 0
			return totalQuantity > 0 ? Math.round((warehouseQuantity / totalQuantity) * 100) : 0
		},
		warehousingProgressClass() {
			const progress = this.warehousingProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		warehouseInQuantity() {
			const quantity = this.orderData.warehousing?.quantity || 0
			return `${quantity}${this.unitName}`
		},

		// ===== 发货计算属性 =====
		deliveryStatusText() {
			const status = this.orderData.delivery?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		deliveryStatusClass() {
			const status = this.orderData.delivery?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		deliveryProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const deliveryQuantity = this.orderData.delivery?.quantity || 0
			return totalQuantity > 0 ? Math.round((deliveryQuantity / totalQuantity) * 100) : 0
		},
		deliveryProgressClass() {
			const progress = this.deliveryProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		deliveredQuantity() {
			const quantity = this.orderData.delivery?.quantity || 0
			return `${quantity}${this.unitName}`
		},

		// ===== 出库计算属性 =====
		outboundStatusText() {
			const status = this.orderData.outbound?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},
		outboundStatusClass() {
			const status = this.orderData.outbound?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},
		outboundProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const outboundQuantity = this.orderData.outbound?.quantity || 0
			return totalQuantity > 0 ? Math.round((outboundQuantity / totalQuantity) * 100) : 0
		},
		outboundProgressClass() {
			const progress = this.outboundProgress
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},
		warehouseOutQuantity() {
			const quantity = this.orderData.outbound?.quantity || 0
			return `${quantity}${this.unitName}`
		},

		// ===== 订单状态计算属性 =====
		orderStatusText() {
			if (this.isOrderException) return '异常'
			if (this.isOrderOverdue) return '逾期'

			const status = this.orderData.status || this.orderData.orderStatus || 'pending'
			const statusMap = {
				'completed': '已完成',
				'processing': '进行中',
				'pending': '待处理',
				'cancelled': '已取消'
			}
			return statusMap[status] || '待处理'
		},
		orderStatusClass() {
			if (this.isOrderException) return 'exception'
			if (this.isOrderOverdue) return 'overdue'

			const status = this.orderData.status || this.orderData.orderStatus || 'pending'
			const classMap = {
				'completed': 'completed',
				'processing': 'processing',
				'pending': 'pending',
				'cancelled': 'cancelled'
			}
			return classMap[status] || 'pending'
		},
		isOrderException() {
			return this.orderData.isException || false
		},
		isOrderOverdue() {
			return this.orderData.isOverdue || false
		},
		exceptionMessage() {
			return this.orderData.exceptionMessage || '订单异常'
		},
		canEditOrder() {
			const status = this.orderData.status || this.orderData.orderStatus || 'pending'
			return ['processing', 'pending'].includes(status) && !this.isOrderException
		}
	},
	methods: {
		// 轮播切换事件
		handleSwiperChange(e) {
			this.currentSwiperIndex = e.detail.current
		},

		// 格式化日期
		formatDate(date) {
			if (!date) return '-'
			const d = new Date(date)
			return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
		},

		// 订单详情
		handleOrderDetail() {
			this.$emit('detail', this.orderData)
		},

		// 编辑订单
		handleEdit() {
			this.$emit('edit', this.orderData)
		},

		// ===== 获取时间相关方法 =====
		getRequestCreateTime() {
			return this.orderData.request?.createTime
		},

		getRequestConfirmTime() {
			return this.orderData.request?.confirmTime
		},

		// ===== 状态文本和样式方法（仍需要的） =====
		getPurchaseItemStatusText(status) {
			const statusMap = {
				'0': '待采购',
				'1': '采购中',
				'2': '已采购',
				'3': '已完成',
				'4': '已入库'
			}
			return statusMap[status] || '未知'
		},

		getPurchaseItemStatusClass(status) {
			const classMap = {
				'0': 'status-pending',
				'1': 'status-processing',
				'2': 'status-purchased',
				'3': 'status-completed',
				'4': 'status-warehoused'
			}
			return classMap[status] || 'status-pending'
		},

		getScheduleStatusText(status) {
			const statusMap = {
				'0': '待开始',
				'1': '进行中',
				'2': '已暂停',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '未知'
		},

		getScheduleStatusClass(status) {
			const classMap = {
				'0': 'status-pending',
				'1': 'status-processing',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		}
	}
}
</script>

<style scoped>
.order-card {
	background: white;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.order-card:active {
	transform: scale(0.98);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16px;
}

.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.order-no {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.order-type-badge {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.order-type-badge.fulfillment {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.order-priority {
	display: inline-block;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.priority-high {
	background-color: #f56c6c;
}

.priority-medium {
	background-color: #e6a23c;
}

.priority-low {
	background-color: #909399;
}

.order-quantity {
	text-align: right;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.quantity-main {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quantity-completed {
	font-size: 12px;
	color: #67c23a;
}

/* 轮播容器样式 */
.order-swiper {
	height: 400px;
	margin-bottom: 16px;
}

.swiper-item {
	padding: 0 4px;
}

.swiper-content {
	height: 100%;
	overflow-y: auto;
}

.section-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 12px;
	text-align: center;
}

/* 信息区块样式 */
.info-section {
	margin-bottom: 16px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 6px;
	margin-bottom: 8px;
}

.section-label {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 8px;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.info-label {
	font-size: 12px;
	color: #666;
}

.info-value {
	font-size: 13px;
	color: #333;
	word-break: break-all;
}

/* 状态信息样式 */
.status-info {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.status-label {
	font-size: 12px;
	color: #666;
}

.status-value {
	font-size: 13px;
	color: #333;
}

.status-tag {
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 11px;
	font-weight: 500;
}

.status-pending {
	background-color: #f0f0f0;
	color: #666;
}

.status-confirmed {
	background-color: #e3f2fd;
	color: #1976d2;
}

.status-processing {
	background-color: #fff3e0;
	color: #f57c00;
}

.status-completed {
	background-color: #e8f5e8;
	color: #4caf50;
}

.status-shortage {
	background-color: #ffebee;
	color: #f44336;
}

.status-sufficient {
	background-color: #e8f5e8;
	color: #4caf50;
}

.status-partial {
	background-color: #fff3e0;
	color: #f57c00;
}

.status-exceeded {
	background-color: #e1f5fe;
	color: #0288d1;
}

.status-paused {
	background-color: #fafafa;
	color: #757575;
}

.status-cancelled {
	background-color: #ffebee;
	color: #f44336;
}

/* 进度条样式 */
.progress-item {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.progress-label {
	font-size: 12px;
	color: #666;
}

.progress-container {
	display: flex;
	align-items: center;
	gap: 8px;
}

.progress-bar {
	flex: 1;
	height: 6px;
	background-color: #e4e7ed;
	border-radius: 3px;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	border-radius: 3px;
	transition: width 0.3s ease;
}

.progress-green {
	background-color: #67c23a;
}

.progress-yellow {
	background-color: #e6a23c;
}

.progress-red {
	background-color: #f56c6c;
}

.progress-text {
	font-size: 11px;
	color: #666;
	min-width: 30px;
	text-align: right;
}

/* 原料列表样式 */
.material-list {
	margin-top: 8px;
}

.material-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 6px 0;
	border-bottom: 1px solid #eee;
}

.material-item:last-child {
	border-bottom: none;
}

.material-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.material-name {
	font-size: 12px;
	color: #333;
}

.material-quantity {
	font-size: 11px;
	color: #666;
}

.material-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
}

.material-status.shortage {
	background-color: #ffebee;
	color: #f44336;
}

.material-status.sufficient {
	background-color: #e8f5e8;
	color: #4caf50;
}

/* 采购列表样式 */
.purchase-list {
	margin-top: 8px;
}

.purchase-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 6px 0;
	border-bottom: 1px solid #eee;
}

.purchase-item:last-child {
	border-bottom: none;
}

.purchase-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.purchase-time {
	font-size: 11px;
	color: #666;
}

.purchase-material {
	font-size: 12px;
	color: #333;
}

.purchase-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
}

/* 计划信息样式 */
.plan-info {
	display: flex;
	flex-direction: column;
	gap: 6px;
	margin-top: 8px;
}

.plan-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.plan-label {
	font-size: 12px;
	color: #666;
}

.plan-value {
	font-size: 12px;
	color: #333;
}

/* 计划安排样式 */
.schedule-list {
	margin-top: 12px;
}

.schedule-title {
	font-size: 13px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
}

.schedule-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 8px 0;
	border-bottom: 1px solid #eee;
}

.schedule-item:last-child {
	border-bottom: none;
}

.schedule-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.schedule-stage {
	font-size: 12px;
	font-weight: 500;
	color: #333;
}

.schedule-time, .schedule-quantity, .schedule-fulfilled {
	font-size: 11px;
	color: #666;
}

.schedule-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
	margin-left: 8px;
}

/* 空信息样式 */
.empty-info {
	text-align: center;
	padding: 20px;
	color: #999;
	font-size: 12px;
}

/* 订单底部样式 */
.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 16px;
	padding-top: 12px;
	border-top: 1px solid #eee;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-right {
	display: flex;
	align-items: center;
}

.order-actions {
	display: flex;
	gap: 8px;
}

.order-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
}

.order-status.pending {
	background-color: #f0f0f0;
	color: #666;
}

.order-status.processing {
	background-color: #e3f2fd;
	color: #409eff;
}

.order-status.completed {
	background-color: #e8f5e8;
	color: #67c23a;
}

.order-status.exception {
	background-color: #ffebee;
	color: #f56c6c;
}

.order-status.overdue {
	background-color: #fff5f5;
	color: #f56c6c;
	border: 1px solid #fecaca;
}

.order-status.cancelled {
	background-color: #fafafa;
	color: #909399;
}

.overdue-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.overdue-text {
	font-size: 12px;
	color: #f56c6c;
}

/* 异常提示样式 */
.exception-alert {
	margin-top: 12px;
	padding: 8px 12px;
	background-color: #fff5f5;
	border: 1px solid #fecaca;
	border-radius: 6px;
	display: flex;
	align-items: center;
}

.exception-text {
	margin-left: 6px;
	font-size: 12px;
	color: #f56c6c;
}

/* 轮播指示器自定义样式 */
.order-swiper ::v-deep .uni-swiper-dots {
	bottom: 8px;
}

.order-swiper ::v-deep .uni-swiper-dot {
	width: 6px;
	height: 6px;
	margin: 0 3px;
}

.order-swiper ::v-deep .uni-swiper-dot-active {
	background-color: #409eff;
}

/* 响应式调整 */
@media (max-width: 375px) {
	.info-grid {
		grid-template-columns: 1fr;
	}

	.order-swiper {
		height: 450px;
	}
}
</style>
