<template>
	<view class="order-card" @click="handleOrderDetail">
		<view class="order-header">
			<view class="order-info">
				<view class="order-no">{{ orderData.orderNo }}</view>
				<view class="order-type-badge fulfillment">履约</view>
				<view class="order-priority" v-if="orderData.priority" :class="'priority-' + orderData.priority">
					{{
						orderData.priority === 'high' ? '高' :
						orderData.priority === 'low' ? '低' : '中'
					}}
				</view>
			</view>
			<view class="order-quantity">
				<text class="quantity-main">{{ orderData.quantity || 0 }}{{ orderData.unitName || '' }}</text>
				<text class="quantity-completed" v-if="orderData.completedQuantity">
					已完成: {{ orderData.completedQuantity }}{{ orderData.unitName || '' }}
				</text>
			</view>
		</view>

		<view class="order-content">
			<view class="customer-info">
				<uv-icon name="account" size="16" color="#909399"></uv-icon>
				<text class="customer-name">{{ orderData.customerName || '未知客户' }}</text>
				<text class="order-date-text" v-if="orderData.orderDate">
					{{ formatDate(orderData.orderDate) }}
				</text>
			</view>
			<view class="product-info">
				<view class="product-main">
					<text class="product-name">{{ orderData.productInfo || '暂无产品信息' }}</text>
					<text class="product-code" v-if="orderData.productCode">{{ orderData.productCode }}</text>
				</view>
				<text class="product-spec" v-if="orderData.productSpec">{{ orderData.productSpec }}</text>
			</view>
			<view class="time-info">
				<view class="delivery-info" v-if="orderData.deliveryTime">
					<uv-icon name="calendar" size="14" color="#909399"></uv-icon>
					<text class="delivery-text">交付: {{ formatDate(orderData.deliveryTime) }}</text>
				</view>
				<view class="requirement-info" v-if="orderData.requirement">
					<text class="requirement-text">{{ orderData.requirement }}</text>
				</view>
			</view>
		</view>

		<view class="order-progress">
			<view class="progress-info">
				<text class="progress-label">整体进度</text>
				<text class="progress-value">{{ orderData.progress || 0 }}%</text>
			</view>
			<uv-line-progress
				:percentage="orderData.progress || 0"
				:show-text="false"
				height="6"
				active-color="#409eff"
				inactive-color="#e4e7ed"
			></uv-line-progress>
		</view>

		<!-- 履约阶段详情 -->
		<view class="fulfillment-details">
			<view class="stage-row">
				<view class="stage-group">
					<view class="stage-item" :class="[
						orderData.materialProgress >= 100 ? 'completed' :
						orderData.materialProgress > 0 ? 'active' : 'pending'
					]">
						<text class="stage-name">物料</text>
						<text class="stage-progress">{{ Math.round(orderData.materialProgress || 0) }}%</text>
					</view>
					<view class="stage-item" :class="[
						orderData.procurementProgress >= 100 ? 'completed' :
						orderData.procurementProgress > 0 ? 'active' : 'pending'
					]">
						<text class="stage-name">采购</text>
						<text class="stage-progress">{{ Math.round(orderData.procurementProgress || 0) }}%</text>
					</view>
					<view class="stage-item" :class="[
						Math.max(orderData.productionPlanProgress || 0, orderData.productionProgress || 0) >= 100 ? 'completed' :
						Math.max(orderData.productionPlanProgress || 0, orderData.productionProgress || 0) > 0 ? 'active' : 'pending'
					]">
						<text class="stage-name">生产</text>
						<text class="stage-progress">{{ Math.round(Math.max(orderData.productionPlanProgress || 0, orderData.productionProgress || 0)) }}%</text>
					</view>
				</view>
				<view class="stage-group">
					<view class="stage-item" :class="[
						orderData.qualityProgress >= 100 ? 'completed' :
						orderData.qualityProgress > 0 ? 'active' : 'pending'
					]">
						<text class="stage-name">质检</text>
						<text class="stage-progress">{{ Math.round(orderData.qualityProgress || 0) }}%</text>
					</view>
					<view class="stage-item" :class="[
						orderData.warehouseProgress >= 100 ? 'completed' :
						orderData.warehouseProgress > 0 ? 'active' : 'pending'
					]">
						<text class="stage-name">入库</text>
						<text class="stage-progress">{{ Math.round(orderData.warehouseProgress || 0) }}%</text>
					</view>
					<view class="stage-item" :class="[
						orderData.deliveryProgress >= 100 ? 'completed' :
						orderData.deliveryProgress > 0 ? 'active' : 'pending'
					]">
						<text class="stage-name">交付</text>
						<text class="stage-progress">{{ Math.round(orderData.deliveryProgress || 0) }}%</text>
					</view>
				</view>
			</view>
		</view>

		<view class="order-footer">
			<view class="footer-left">
				<view class="order-status" :class="[
					orderData.isException ? 'exception' :
					orderData.isOverdue ? 'overdue' :
					orderData.status || 'pending'
				]">
					{{
						orderData.isException ? '异常' :
						orderData.isOverdue ? '逾期' :
						orderData.status === 'completed' ? '已完成' :
						orderData.status === 'processing' ? '进行中' : '待处理'
					}}
				</view>
				<view class="order-actions">
					<uv-button
						size="mini"
						type="primary"
						plain
						@click.stop="handleOrderDetail"
					>
						详情
					</uv-button>
					<uv-button
						v-if="['processing', 'pending'].includes(orderData.status) && !orderData.isException"
						size="mini"
						type="warning"
						plain
						@click.stop="handleEdit"
					>
						编辑
					</uv-button>
				</view>
			</view>
			<view class="footer-right">
				<view class="overdue-info" v-if="orderData.isOverdue">
					<uv-icon name="warning" size="12" color="#f56c6c"></uv-icon>
					<text class="overdue-text">逾期</text>
				</view>
			</view>
		</view>

		<!-- 异常提示 -->
		<view v-if="orderData.isException" class="exception-alert">
			<uv-icon name="error-circle" size="14" color="#f56c6c"></uv-icon>
			<text class="exception-text">{{ orderData.exceptionMessage }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'OrderProcessItem',
	props: {
		orderData: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	methods: {
		// 格式化日期
		formatDate(date) {
			if (!date) return ''
			const d = new Date(date)
			return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
		},
		
		// 订单详情
		handleOrderDetail() {
			this.$emit('detail', this.orderData)
		},
		
		// 编辑订单
		handleEdit() {
			this.$emit('edit', this.orderData)
		}
	}
}
</script>

<style scoped>
.order-card {
	background: white;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.order-card:active {
	transform: scale(0.98);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
}

.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.order-no {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.order-type-badge {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.order-type-badge.fulfillment {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.order-priority {
	display: inline-block;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.priority-high {
	background-color: #f56c6c;
}

.priority-medium {
	background-color: #e6a23c;
}

.priority-low {
	background-color: #909399;
}

.order-quantity {
	text-align: right;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.quantity-main {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quantity-completed {
	font-size: 12px;
	color: #67c23a;
}

.order-content {
	margin-bottom: 12px;
}

.customer-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}

.customer-name {
	margin-left: 6px;
	font-size: 14px;
	color: #666;
}

.order-date-text {
	font-size: 12px;
	color: #999;
}

.product-info {
	margin-bottom: 8px;
}

.product-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4px;
}

.product-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.product-code {
	font-size: 12px;
	color: #666;
	background: #f5f5f5;
	padding: 2px 6px;
	border-radius: 4px;
}

.product-spec {
	font-size: 12px;
	color: #999;
	margin-top: 2px;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.delivery-info, .requirement-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.delivery-text, .requirement-text {
	font-size: 12px;
	color: #666;
}

.requirement-text {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 进度条 */
.order-progress {
	margin-bottom: 12px;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 6px;
}

.progress-label {
	font-size: 14px;
	color: #666;
}

.progress-value {
	font-size: 14px;
	font-weight: 500;
	color: #409eff;
}

/* 履约阶段详情样式 */
.fulfillment-details {
	margin: 12px 0;
	padding: 12px;
	background: #fafafa;
	border-radius: 8px;
}

.stage-row {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.stage-group {
	display: flex;
	gap: 8px;
	justify-content: space-between;
}

.stage-item {
	flex: 1;
	padding: 8px 6px;
	border-radius: 6px;
	background: #f5f5f5;
	border: 1px solid #e0e0e0;
	text-align: center;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.stage-item.pending {
	background: #f5f5f5;
	color: #999;
	border-color: #e0e0e0;
}

.stage-item.active {
	background: #e3f2fd;
	color: #1976d2;
	border-color: #1976d2;
}

.stage-item.completed {
	background: #e8f5e8;
	color: #4caf50;
	border-color: #4caf50;
}

.stage-name {
	font-size: 11px;
	font-weight: 500;
}

.stage-progress {
	font-size: 10px;
	opacity: 0.8;
}

/* 订单底部样式 */
.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 12px;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-right {
	display: flex;
	align-items: center;
}

.order-actions {
	display: flex;
	gap: 8px;
}

.order-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
}

.order-status.pending {
	background-color: #f0f0f0;
	color: #666;
}

.order-status.processing {
	background-color: #e3f2fd;
	color: #409eff;
}

.order-status.completed {
	background-color: #e8f5e8;
	color: #67c23a;
}

.order-status.exception {
	background-color: #ffebee;
	color: #f56c6c;
}

.order-status.overdue {
	background-color: #fff5f5;
	color: #f56c6c;
	border: 1px solid #fecaca;
}

.overdue-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.overdue-text {
	font-size: 12px;
	color: #f56c6c;
}

/* 异常提示 */
.exception-alert {
	margin-top: 12px;
	padding: 8px 12px;
	background-color: #fff5f5;
	border: 1px solid #fecaca;
	border-radius: 6px;
	display: flex;
	align-items: center;
}

.exception-text {
	margin-left: 6px;
	font-size: 12px;
	color: #f56c6c;
}
</style>
