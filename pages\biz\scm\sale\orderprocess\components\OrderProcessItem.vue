<template>
	<view class="order-card">
		<!-- 订单头部信息 -->
		<view class="order-header">
			<view class="order-info">
				<view class="order-no">{{ getOrderNo() }}</view>
				<view class="order-type-badge fulfillment">履约</view>
				<view class="order-priority" v-if="orderData.priority" :class="'priority-' + orderData.priority">
					{{
						orderData.priority === 'high' ? '高' :
						orderData.priority === 'low' ? '低' : '中'
					}}
				</view>
			</view>
			<view class="order-quantity">
				<text class="quantity-main">{{ getMainQuantity() }}</text>
				<text class="quantity-completed" v-if="getCompletedQuantity()">
					已完成: {{ getCompletedQuantity() }}
				</text>
			</view>
		</view>

		<!-- 轮播容器 -->
		<swiper
			class="order-swiper"
			:indicator-dots="true"
			:autoplay="false"
			:circular="false"
			indicator-color="rgba(0, 0, 0, .3)"
			indicator-active-color="#409eff"
			@change="handleSwiperChange"
		>
			<!-- 第一页：订单信息、产品信息、数量、需求状态 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">基本信息</view>

					<!-- 订单信息 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="file-text" size="16" color="#409eff"></uv-icon>
							<text class="section-label">订单信息</text>
						</view>
						<view class="info-grid">
							<view class="info-item">
								<text class="info-label">客户名称</text>
								<text class="info-value">{{ getCustomerName() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">下单时间</text>
								<text class="info-value">{{ formatDate(getOrderDate()) }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">发货时间</text>
								<text class="info-value">{{ formatDate(getDeliveryTime()) }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">要求</text>
								<text class="info-value">{{ getRequirement() }}</text>
							</view>
						</view>
					</view>

					<!-- 产品信息 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="shopping-bag" size="16" color="#409eff"></uv-icon>
							<text class="section-label">产品信息</text>
						</view>
						<view class="info-grid">
							<view class="info-item">
								<text class="info-label">产品名称</text>
								<text class="info-value">{{ getProductName() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">产品编码</text>
								<text class="info-value">{{ getProductCode() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">规格</text>
								<text class="info-value">{{ getProductSpec() }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">数量</text>
								<text class="info-value">{{ getMainQuantity() }}</text>
							</view>
						</view>
					</view>

					<!-- 需求状态 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="checkmark-circle" size="16" color="#409eff"></uv-icon>
							<text class="section-label">需求状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getRequestStatusClass()">
									{{ getRequestStatusText() }}
								</view>
							</view>
							<view class="status-item" v-if="getRequestCreateTime()">
								<text class="status-label">创建时间</text>
								<text class="status-value">{{ formatDate(getRequestCreateTime()) }}</text>
							</view>
							<view class="status-item" v-if="getRequestConfirmTime()">
								<text class="status-label">BOM确认时间</text>
								<text class="status-value">{{ formatDate(getRequestConfirmTime()) }}</text>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第二页：原料库存 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">原料库存</view>

					<view class="info-section">
						<view class="section-header">
							<uv-icon name="layers" size="16" color="#409eff"></uv-icon>
							<text class="section-label">库存状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getMaterialStatusClass()">
									{{ getMaterialStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">库存进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getMaterialProgressClass()" :style="{ width: getMaterialProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getMaterialProgress() }}%</text>
								</view>
							</view>
						</view>

						<!-- 原料详情 -->
						<view class="material-list" v-if="getMaterialList().length > 0">
							<view class="material-item" v-for="(material, index) in getMaterialList()" :key="index">
								<view class="material-info">
									<text class="material-name">{{ material.fullCode }} {{ material.name }}</text>
									<text class="material-quantity">{{ material.pendingQty || 0 }} {{ material.unitName || '' }}</text>
								</view>
								<view class="material-status" :class="material.shortage ? 'shortage' : 'sufficient'">
									{{ material.shortage ? ('缺料' + (material.shortageQty || 0)) : '已备齐' }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无原料信息</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第三页：采购 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">采购</view>

					<view class="info-section">
						<view class="section-header">
							<uv-icon name="shopping-cart" size="16" color="#409eff"></uv-icon>
							<text class="section-label">采购状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getProcurementStatusClass()">
									{{ getProcurementStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">采购进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getProcurementProgressClass()" :style="{ width: getProcurementProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getProcurementProgress() }}%</text>
								</view>
							</view>
						</view>

						<!-- 采购详情 -->
						<view class="purchase-list" v-if="getPurchaseList().length > 0">
							<view class="purchase-item" v-for="(purchase, index) in getPurchaseList()" :key="index">
								<view class="purchase-info">
									<text class="purchase-time">{{ formatDate(purchase.purchaseTime) }}</text>
									<text class="purchase-material">{{ purchase.materialCode }} {{ purchase.materialName }}</text>
								</view>
								<view class="purchase-status" :class="getPurchaseItemStatusClass(purchase.purchaseStatus)">
									{{ getPurchaseItemStatusText(purchase.purchaseStatus) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无采购信息</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第四页：计划 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">生产计划</view>

					<view class="info-section">
						<view class="section-header">
							<uv-icon name="calendar" size="16" color="#409eff"></uv-icon>
							<text class="section-label">计划状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getProductionPlanStatusClass()">
									{{ getProductionPlanStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">计划进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getProductionPlanProgressClass()" :style="{ width: getProductionPlanProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getProductionPlanProgress() }}%</text>
								</view>
							</view>
						</view>

						<!-- 计划详情 -->
						<view class="plan-info">
							<view class="plan-item">
								<text class="plan-label">车间</text>
								<text class="plan-value">{{ getWorkshop() }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">产线</text>
								<text class="plan-value">{{ getProductionLine() }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">负责人</text>
								<text class="plan-value">{{ getForeman() }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">计划数量</text>
								<text class="plan-value">{{ getPlanQuantity() }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">已完成</text>
								<text class="plan-value">{{ getFulfilledQuantity() }}</text>
							</view>
						</view>

						<!-- 计划安排 -->
						<view class="schedule-list" v-if="getScheduleList().length > 0">
							<view class="schedule-title">计划安排</view>
							<view class="schedule-item" v-for="(schedule, index) in getScheduleList()" :key="index">
								<view class="schedule-info">
									<text class="schedule-stage">第{{ index + 1 }}阶段</text>
									<text class="schedule-time">{{ formatDate(schedule.startTime) }} ~ {{ formatDate(schedule.endTime) }}</text>
									<text class="schedule-quantity">计划: {{ schedule.quantity || 0 }}{{ getUnitName() }}</text>
									<text class="schedule-fulfilled">已完成: {{ schedule.fulfilledQty || 0 }}{{ getUnitName() }}</text>
								</view>
								<view class="schedule-status" :class="getScheduleStatusClass(schedule.status)">
									{{ getScheduleStatusText(schedule.status) }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第五页：生产、质检、入库、发货、出库 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">
					<view class="section-title">执行状态</view>

					<!-- 生产执行 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="settings" size="16" color="#409eff"></uv-icon>
							<text class="section-label">生产执行</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getProductionExecutionStatusClass()">
									{{ getProductionExecutionStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">生产进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getProductionExecutionProgressClass()" :style="{ width: getProductionExecutionProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getProductionExecutionProgress() }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已生产</text>
								<text class="status-value">{{ getProductionQuantity() }}</text>
							</view>
						</view>
					</view>

					<!-- 质检 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="checkmark-circle" size="16" color="#409eff"></uv-icon>
							<text class="section-label">质检</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getQualityInspectionStatusClass()">
									{{ getQualityInspectionStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">质检进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getQualityInspectionProgressClass()" :style="{ width: getQualityInspectionProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getQualityInspectionProgress() }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已质检</text>
								<text class="status-value">{{ getQualityInspectedQuantity() }}</text>
							</view>
						</view>
					</view>

					<!-- 入库 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="archive" size="16" color="#409eff"></uv-icon>
							<text class="section-label">入库</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getWarehousingStatusClass()">
									{{ getWarehousingStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">入库进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getWarehousingProgressClass()" :style="{ width: getWarehousingProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getWarehousingProgress() }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已入库</text>
								<text class="status-value">{{ getWarehouseInQuantity() }}</text>
							</view>
						</view>
					</view>

					<!-- 发货 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="truck" size="16" color="#409eff"></uv-icon>
							<text class="section-label">发货</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getDeliveryStatusClass()">
									{{ getDeliveryStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">发货进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getDeliveryProgressClass()" :style="{ width: getDeliveryProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getDeliveryProgress() }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已发货</text>
								<text class="status-value">{{ getDeliveredQuantity() }}</text>
							</view>
						</view>
					</view>

					<!-- 出库 -->
					<view class="info-section">
						<view class="section-header">
							<uv-icon name="share" size="16" color="#409eff"></uv-icon>
							<text class="section-label">出库</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view class="status-tag" :class="getOutboundStatusClass()">
									{{ getOutboundStatusText() }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">出库进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view class="progress-fill" :class="getOutboundProgressClass()" :style="{ width: getOutboundProgress() + '%' }"></view>
									</view>
									<text class="progress-text">{{ getOutboundProgress() }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已出库</text>
								<text class="status-value">{{ getWarehouseOutQuantity() }}</text>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>

		<!-- 订单底部操作 -->
		<view class="order-footer">
			<view class="footer-left">
				<view class="order-status" :class="getOrderStatusClass()">
					{{ getOrderStatusText() }}
				</view>
				<view class="order-actions">
					<uv-button
						size="mini"
						type="primary"
						plain
						@click.stop="handleOrderDetail"
					>
						详情
					</uv-button>
					<uv-button
						v-if="canEdit()"
						size="mini"
						type="warning"
						plain
						@click.stop="handleEdit"
					>
						编辑
					</uv-button>
				</view>
			</view>
			<view class="footer-right">
				<view class="overdue-info" v-if="isOverdue()">
					<uv-icon name="warning" size="12" color="#f56c6c"></uv-icon>
					<text class="overdue-text">逾期</text>
				</view>
			</view>
		</view>

		<!-- 异常提示 -->
		<view v-if="isException()" class="exception-alert">
			<uv-icon name="error-circle" size="14" color="#f56c6c"></uv-icon>
			<text class="exception-text">{{ getExceptionMessage() }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'OrderProcessItem',
	props: {
		orderData: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			currentSwiperIndex: 0
		}
	},
	methods: {
		// 轮播切换事件
		handleSwiperChange(e) {
			this.currentSwiperIndex = e.detail.current
		},

		// 格式化日期
		formatDate(date) {
			if (!date) return '-'
			const d = new Date(date)
			return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
		},

		// 订单详情
		handleOrderDetail() {
			this.$emit('detail', this.orderData)
		},

		// 编辑订单
		handleEdit() {
			this.$emit('edit', this.orderData)
		},

		// ===== 基本信息获取方法 =====
		getOrderNo() {
			return this.orderData.salesOrderInfo?.orderNo || this.orderData.orderNo || 'N/A'
		},

		getCustomerName() {
			return this.orderData.salesOrderInfo?.customer?.name || this.orderData.customerName || '未知客户'
		},

		getOrderDate() {
			return this.orderData.salesOrderInfo?.orderDate || this.orderData.orderDate
		},

		getDeliveryTime() {
			return this.orderData.salesOrderInfo?.deliveryTime || this.orderData.deliveryTime
		},

		getRequirement() {
			return this.orderData.salesOrderInfo?.requirement || this.orderData.salesOrderInfo?.remark || this.orderData.requirement || '-'
		},

		getProductName() {
			return this.orderData.salesOrderInfo?.product?.name || this.orderData.productInfo || '暂无产品信息'
		},

		getProductCode() {
			return this.orderData.salesOrderInfo?.product?.fullCode || this.orderData.productCode || '-'
		},

		getProductSpec() {
			return this.orderData.salesOrderInfo?.product?.spec || this.orderData.productSpec || '-'
		},

		getMainQuantity() {
			const quantity = this.orderData.salesOrderInfo?.product?.quantity || this.orderData.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		getCompletedQuantity() {
			const quantity = this.orderData.outbound?.quantity || this.orderData.completedQuantity || 0
			const unit = this.getUnitName()
			return quantity > 0 ? `${quantity}${unit}` : ''
		},

		getUnitName() {
			return this.orderData.salesOrderInfo?.product?.unitName || this.orderData.unitName || ''
		},

		// ===== 需求状态相关方法 =====
		getRequestStatusText() {
			const status = this.orderData.request?.status || '0'
			const statusMap = {
				'0': '待分析',
				'1': '已确认',
				'2': '进行中',
				'3': '已完成'
			}
			return statusMap[status] || '未知'
		},

		getRequestStatusClass() {
			const status = this.orderData.request?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-confirmed',
				'2': 'status-processing',
				'3': 'status-completed'
			}
			return classMap[status] || 'status-pending'
		},

		getRequestCreateTime() {
			return this.orderData.request?.createTime
		},

		getRequestConfirmTime() {
			return this.orderData.request?.confirmTime
		},

		// ===== 原料库存相关方法 =====
		getMaterialStatusText() {
			const status = this.orderData.materialInventory?.status || '0'
			const statusMap = {
				'0': '待分析',
				'1': '欠缺',
				'2': '已满足'
			}
			return statusMap[status] || '未知'
		},

		getMaterialStatusClass() {
			const status = this.orderData.materialInventory?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-shortage',
				'2': 'status-sufficient'
			}
			return classMap[status] || 'status-pending'
		},

		getMaterialProgress() {
			return Math.round(this.orderData.materialInventory?.progress || 0)
		},

		getMaterialProgressClass() {
			const progress = this.getMaterialProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getMaterialList() {
			return this.orderData.materialInventory?.rawMaterials || []
		},

		// ===== 采购相关方法 =====
		getProcurementStatusText() {
			const status = this.orderData.procurement?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getProcurementStatusClass() {
			const status = this.orderData.procurement?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getProcurementProgress() {
			return Math.round(this.orderData.procurement?.progress || 0)
		},

		getProcurementProgressClass() {
			const progress = this.getProcurementProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getPurchaseList() {
			return this.orderData.procurement?.purchaseItems || []
		},

		getPurchaseItemStatusText(status) {
			const statusMap = {
				'0': '待采购',
				'1': '采购中',
				'2': '已采购',
				'3': '已完成',
				'4': '已入库'
			}
			return statusMap[status] || '未知'
		},

		getPurchaseItemStatusClass(status) {
			const classMap = {
				'0': 'status-pending',
				'1': 'status-processing',
				'2': 'status-purchased',
				'3': 'status-completed',
				'4': 'status-warehoused'
			}
			return classMap[status] || 'status-pending'
		},

		// ===== 生产计划相关方法 =====
		getProductionPlanStatusText() {
			const status = this.orderData.productionPlan?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getProductionPlanStatusClass() {
			const status = this.orderData.productionPlan?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getProductionPlanProgress() {
			return Math.round(this.orderData.productionPlan?.progress || 0)
		},

		getProductionPlanProgressClass() {
			const progress = this.getProductionPlanProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getWorkshop() {
			return this.orderData.productionPlan?.workshop || '-'
		},

		getProductionLine() {
			return this.orderData.productionPlan?.productionLine || '-'
		},

		getForeman() {
			return this.orderData.productionPlan?.foreman || '-'
		},

		getPlanQuantity() {
			const quantity = this.orderData.productionPlan?.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		getFulfilledQuantity() {
			const quantity = this.orderData.productionPlan?.fulfilledQty || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		getScheduleList() {
			return this.orderData.productionPlan?.schedule || []
		},

		getScheduleStatusText(status) {
			const statusMap = {
				'0': '待开始',
				'1': '进行中',
				'2': '已暂停',
				'3': '已完成',
				'4': '已取消'
			}
			return statusMap[status] || '未知'
		},

		getScheduleStatusClass(status) {
			const classMap = {
				'0': 'status-pending',
				'1': 'status-processing',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return classMap[status] || 'status-pending'
		},

		// ===== 生产执行相关方法 =====
		getProductionExecutionStatusText() {
			const status = this.orderData.productionExecution?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getProductionExecutionStatusClass() {
			const status = this.orderData.productionExecution?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getProductionExecutionProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const productionQuantity = this.orderData.productionExecution?.quantity || 0
			return totalQuantity > 0 ? Math.round((productionQuantity / totalQuantity) * 100) : 0
		},

		getProductionExecutionProgressClass() {
			const progress = this.getProductionExecutionProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getProductionQuantity() {
			const quantity = this.orderData.productionExecution?.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		// ===== 质检相关方法 =====
		getQualityInspectionStatusText() {
			const status = this.orderData.qualityInspection?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getQualityInspectionStatusClass() {
			const status = this.orderData.qualityInspection?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getQualityInspectionProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const inspectedQuantity = this.orderData.qualityInspection?.quantity || 0
			return totalQuantity > 0 ? Math.round((inspectedQuantity / totalQuantity) * 100) : 0
		},

		getQualityInspectionProgressClass() {
			const progress = this.getQualityInspectionProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getQualityInspectedQuantity() {
			const quantity = this.orderData.qualityInspection?.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		// ===== 入库相关方法 =====
		getWarehousingStatusText() {
			const status = this.orderData.warehousing?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getWarehousingStatusClass() {
			const status = this.orderData.warehousing?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getWarehousingProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const warehouseQuantity = this.orderData.warehousing?.quantity || 0
			return totalQuantity > 0 ? Math.round((warehouseQuantity / totalQuantity) * 100) : 0
		},

		getWarehousingProgressClass() {
			const progress = this.getWarehousingProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getWarehouseInQuantity() {
			const quantity = this.orderData.warehousing?.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		// ===== 发货相关方法 =====
		getDeliveryStatusText() {
			const status = this.orderData.delivery?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getDeliveryStatusClass() {
			const status = this.orderData.delivery?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getDeliveryProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const deliveryQuantity = this.orderData.delivery?.quantity || 0
			return totalQuantity > 0 ? Math.round((deliveryQuantity / totalQuantity) * 100) : 0
		},

		getDeliveryProgressClass() {
			const progress = this.getDeliveryProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getDeliveredQuantity() {
			const quantity = this.orderData.delivery?.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		// ===== 出库相关方法 =====
		getOutboundStatusText() {
			const status = this.orderData.outbound?.status || '0'
			const statusMap = {
				'0': '未开始',
				'1': '全部完成',
				'2': '已开始',
				'3': '部分完成',
				'4': '超额完成'
			}
			return statusMap[status] || '未知'
		},

		getOutboundStatusClass() {
			const status = this.orderData.outbound?.status || '0'
			const classMap = {
				'0': 'status-pending',
				'1': 'status-completed',
				'2': 'status-processing',
				'3': 'status-partial',
				'4': 'status-exceeded'
			}
			return classMap[status] || 'status-pending'
		},

		getOutboundProgress() {
			const totalQuantity = this.orderData.salesOrderInfo?.product?.quantity || 0
			const outboundQuantity = this.orderData.outbound?.quantity || 0
			return totalQuantity > 0 ? Math.round((outboundQuantity / totalQuantity) * 100) : 0
		},

		getOutboundProgressClass() {
			const progress = this.getOutboundProgress()
			return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
		},

		getWarehouseOutQuantity() {
			const quantity = this.orderData.outbound?.quantity || 0
			const unit = this.getUnitName()
			return `${quantity}${unit}`
		},

		// ===== 订单状态相关方法 =====
		getOrderStatusText() {
			if (this.isException()) return '异常'
			if (this.isOverdue()) return '逾期'

			const status = this.orderData.status || this.orderData.orderStatus || 'pending'
			const statusMap = {
				'completed': '已完成',
				'processing': '进行中',
				'pending': '待处理',
				'cancelled': '已取消'
			}
			return statusMap[status] || '待处理'
		},

		getOrderStatusClass() {
			if (this.isException()) return 'exception'
			if (this.isOverdue()) return 'overdue'

			const status = this.orderData.status || this.orderData.orderStatus || 'pending'
			const classMap = {
				'completed': 'completed',
				'processing': 'processing',
				'pending': 'pending',
				'cancelled': 'cancelled'
			}
			return classMap[status] || 'pending'
		},

		isException() {
			return this.orderData.isException || false
		},

		isOverdue() {
			return this.orderData.isOverdue || false
		},

		getExceptionMessage() {
			return this.orderData.exceptionMessage || '订单异常'
		},

		canEdit() {
			const status = this.orderData.status || this.orderData.orderStatus || 'pending'
			return ['processing', 'pending'].includes(status) && !this.isException()
		}
	}
}
</script>

<style scoped>
.order-card {
	background: white;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.order-card:active {
	transform: scale(0.98);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16px;
}

.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.order-no {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.order-type-badge {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.order-type-badge.fulfillment {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.order-priority {
	display: inline-block;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	color: white;
}

.priority-high {
	background-color: #f56c6c;
}

.priority-medium {
	background-color: #e6a23c;
}

.priority-low {
	background-color: #909399;
}

.order-quantity {
	text-align: right;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.quantity-main {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quantity-completed {
	font-size: 12px;
	color: #67c23a;
}

/* 轮播容器样式 */
.order-swiper {
	height: 400px;
	margin-bottom: 16px;
}

.swiper-item {
	padding: 0 4px;
}

.swiper-content {
	height: 100%;
	overflow-y: auto;
}

.section-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 12px;
	text-align: center;
}

/* 信息区块样式 */
.info-section {
	margin-bottom: 16px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 6px;
	margin-bottom: 8px;
}

.section-label {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 8px;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.info-label {
	font-size: 12px;
	color: #666;
}

.info-value {
	font-size: 13px;
	color: #333;
	word-break: break-all;
}

/* 状态信息样式 */
.status-info {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.status-label {
	font-size: 12px;
	color: #666;
}

.status-value {
	font-size: 13px;
	color: #333;
}

.status-tag {
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 11px;
	font-weight: 500;
}

.status-pending {
	background-color: #f0f0f0;
	color: #666;
}

.status-confirmed {
	background-color: #e3f2fd;
	color: #1976d2;
}

.status-processing {
	background-color: #fff3e0;
	color: #f57c00;
}

.status-completed {
	background-color: #e8f5e8;
	color: #4caf50;
}

.status-shortage {
	background-color: #ffebee;
	color: #f44336;
}

.status-sufficient {
	background-color: #e8f5e8;
	color: #4caf50;
}

.status-partial {
	background-color: #fff3e0;
	color: #f57c00;
}

.status-exceeded {
	background-color: #e1f5fe;
	color: #0288d1;
}

.status-paused {
	background-color: #fafafa;
	color: #757575;
}

.status-cancelled {
	background-color: #ffebee;
	color: #f44336;
}

/* 进度条样式 */
.progress-item {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.progress-label {
	font-size: 12px;
	color: #666;
}

.progress-container {
	display: flex;
	align-items: center;
	gap: 8px;
}

.progress-bar {
	flex: 1;
	height: 6px;
	background-color: #e4e7ed;
	border-radius: 3px;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	border-radius: 3px;
	transition: width 0.3s ease;
}

.progress-green {
	background-color: #67c23a;
}

.progress-yellow {
	background-color: #e6a23c;
}

.progress-red {
	background-color: #f56c6c;
}

.progress-text {
	font-size: 11px;
	color: #666;
	min-width: 30px;
	text-align: right;
}

/* 原料列表样式 */
.material-list {
	margin-top: 8px;
}

.material-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 6px 0;
	border-bottom: 1px solid #eee;
}

.material-item:last-child {
	border-bottom: none;
}

.material-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.material-name {
	font-size: 12px;
	color: #333;
}

.material-quantity {
	font-size: 11px;
	color: #666;
}

.material-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
}

.material-status.shortage {
	background-color: #ffebee;
	color: #f44336;
}

.material-status.sufficient {
	background-color: #e8f5e8;
	color: #4caf50;
}

/* 采购列表样式 */
.purchase-list {
	margin-top: 8px;
}

.purchase-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 6px 0;
	border-bottom: 1px solid #eee;
}

.purchase-item:last-child {
	border-bottom: none;
}

.purchase-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.purchase-time {
	font-size: 11px;
	color: #666;
}

.purchase-material {
	font-size: 12px;
	color: #333;
}

.purchase-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
}

/* 计划信息样式 */
.plan-info {
	display: flex;
	flex-direction: column;
	gap: 6px;
	margin-top: 8px;
}

.plan-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.plan-label {
	font-size: 12px;
	color: #666;
}

.plan-value {
	font-size: 12px;
	color: #333;
}

/* 计划安排样式 */
.schedule-list {
	margin-top: 12px;
}

.schedule-title {
	font-size: 13px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
}

.schedule-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 8px 0;
	border-bottom: 1px solid #eee;
}

.schedule-item:last-child {
	border-bottom: none;
}

.schedule-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.schedule-stage {
	font-size: 12px;
	font-weight: 500;
	color: #333;
}

.schedule-time, .schedule-quantity, .schedule-fulfilled {
	font-size: 11px;
	color: #666;
}

.schedule-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
	margin-left: 8px;
}

/* 空信息样式 */
.empty-info {
	text-align: center;
	padding: 20px;
	color: #999;
	font-size: 12px;
}

/* 订单底部样式 */
.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 16px;
	padding-top: 12px;
	border-top: 1px solid #eee;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-right {
	display: flex;
	align-items: center;
}

.order-actions {
	display: flex;
	gap: 8px;
}

.order-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
}

.order-status.pending {
	background-color: #f0f0f0;
	color: #666;
}

.order-status.processing {
	background-color: #e3f2fd;
	color: #409eff;
}

.order-status.completed {
	background-color: #e8f5e8;
	color: #67c23a;
}

.order-status.exception {
	background-color: #ffebee;
	color: #f56c6c;
}

.order-status.overdue {
	background-color: #fff5f5;
	color: #f56c6c;
	border: 1px solid #fecaca;
}

.order-status.cancelled {
	background-color: #fafafa;
	color: #909399;
}

.overdue-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.overdue-text {
	font-size: 12px;
	color: #f56c6c;
}

/* 异常提示样式 */
.exception-alert {
	margin-top: 12px;
	padding: 8px 12px;
	background-color: #fff5f5;
	border: 1px solid #fecaca;
	border-radius: 6px;
	display: flex;
	align-items: center;
}

.exception-text {
	margin-left: 6px;
	font-size: 12px;
	color: #f56c6c;
}

/* 轮播指示器自定义样式 */
.order-swiper ::v-deep .uni-swiper-dots {
	bottom: 8px;
}

.order-swiper ::v-deep .uni-swiper-dot {
	width: 6px;
	height: 6px;
	margin: 0 3px;
}

.order-swiper ::v-deep .uni-swiper-dot-active {
	background-color: #409eff;
}

/* 响应式调整 */
@media (max-width: 375px) {
	.info-grid {
		grid-template-columns: 1fr;
	}

	.order-swiper {
		height: 450px;
	}
}

.order-content {
	margin-bottom: 12px;
}

.customer-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;
}

.customer-name {
	margin-left: 6px;
	font-size: 14px;
	color: #666;
}

.order-date-text {
	font-size: 12px;
	color: #999;
}

.product-info {
	margin-bottom: 8px;
}

.product-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4px;
}

.product-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.product-code {
	font-size: 12px;
	color: #666;
	background: #f5f5f5;
	padding: 2px 6px;
	border-radius: 4px;
}

.product-spec {
	font-size: 12px;
	color: #999;
	margin-top: 2px;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.delivery-info, .requirement-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.delivery-text, .requirement-text {
	font-size: 12px;
	color: #666;
}

.requirement-text {
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 进度条 */
.order-progress {
	margin-bottom: 12px;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 6px;
}

.progress-label {
	font-size: 14px;
	color: #666;
}

.progress-value {
	font-size: 14px;
	font-weight: 500;
	color: #409eff;
}

/* 履约阶段详情样式 */
.fulfillment-details {
	margin: 12px 0;
	padding: 12px;
	background: #fafafa;
	border-radius: 8px;
}

.stage-row {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.stage-group {
	display: flex;
	gap: 8px;
	justify-content: space-between;
}

.stage-item {
	flex: 1;
	padding: 8px 6px;
	border-radius: 6px;
	background: #f5f5f5;
	border: 1px solid #e0e0e0;
	text-align: center;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.stage-item.pending {
	background: #f5f5f5;
	color: #999;
	border-color: #e0e0e0;
}

.stage-item.active {
	background: #e3f2fd;
	color: #1976d2;
	border-color: #1976d2;
}

.stage-item.completed {
	background: #e8f5e8;
	color: #4caf50;
	border-color: #4caf50;
}

.stage-name {
	font-size: 11px;
	font-weight: 500;
}

.stage-progress {
	font-size: 10px;
	opacity: 0.8;
}

/* 订单底部样式 */
.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 12px;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-right {
	display: flex;
	align-items: center;
}

.order-actions {
	display: flex;
	gap: 8px;
}

.order-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
}

.order-status.pending {
	background-color: #f0f0f0;
	color: #666;
}

.order-status.processing {
	background-color: #e3f2fd;
	color: #409eff;
}

.order-status.completed {
	background-color: #e8f5e8;
	color: #67c23a;
}

.order-status.exception {
	background-color: #ffebee;
	color: #f56c6c;
}

.order-status.overdue {
	background-color: #fff5f5;
	color: #f56c6c;
	border: 1px solid #fecaca;
}

.overdue-info {
	display: flex;
	align-items: center;
	gap: 4px;
}

.overdue-text {
	font-size: 12px;
	color: #f56c6c;
}

/* 异常提示 */
.exception-alert {
	margin-top: 12px;
	padding: 8px 12px;
	background-color: #fff5f5;
	border: 1px solid #fecaca;
	border-radius: 6px;
	display: flex;
	align-items: center;
}

.exception-text {
	margin-left: 6px;
	font-size: 12px;
	color: #f56c6c;
}
</style>
